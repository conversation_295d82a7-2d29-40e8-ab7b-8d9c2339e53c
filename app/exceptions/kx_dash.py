from .base import ApplicationError


__all__ = ['KXDashDateParsingError', 'MissingClientNameError']


class KXDashDateParsingError(ApplicationError):
    """Raised when date string parsing fails."""

    def __init__(self, date_string: str):
        super().__init__(f'Failed to parse date string in KX Dash response: {date_string}')


class MissingClientNameError(ApplicationError):
    """Raised when client name is missing."""

    def __init__(self, dash_activity_id: int):
        super().__init__(f'Client name is missing for a dash activity: {dash_activity_id}')
